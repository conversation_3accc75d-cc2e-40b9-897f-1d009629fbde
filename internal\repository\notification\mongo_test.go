package notification

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TestUserActivityState_CRUD tests basic CRUD operations for user activity states
func TestUserActivityState_CRUD(t *testing.T) {
	// Skip if no MongoDB connection available
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup test database connection
	ctx := context.Background()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI("mongodb://localhost:27017"))
	require.NoError(t, err)
	defer client.Disconnect(ctx)

	// Use test database
	db := client.Database("dinbora_test_notifications")
	defer db.Drop(ctx) // Clean up after test

	// Create repository
	repo := New(db)

	// Test data
	userID := "test-user-123"
	state := &notification.UserActivityState{
		UserID:                  userID,
		LastLoginAt:             time.Now().Add(-1 * time.Hour),
		LastExpenseLoggedAt:     time.Now().Add(-2 * time.Hour),
		LastProgressionActivity: time.Now().Add(-3 * time.Hour),
		OnboardingState: notification.OnboardingState{
			StartedDiagnosisAt:    time.Now().Add(-24 * time.Hour),
			CompletedDiagnosisAt:  time.Now().Add(-20 * time.Hour),
			CreatedFirstDreamAt:   time.Now().Add(-18 * time.Hour),
			CreatedFirstBudgetAt:  time.Now().Add(-16 * time.Hour),
			CompletedFirstTrailAt: time.Now().Add(-12 * time.Hour),
		},
		NotificationState: notification.NotificationState{
			SentUnique: map[string]time.Time{
				"DIAGNOSTICO_FINANCEIRO": time.Now().Add(-10 * time.Hour),
			},
			LastSentAt: time.Now().Add(-5 * time.Hour),
			TotalSent:  1,
		},
	}

	// Test Create
	t.Run("Create", func(t *testing.T) {
		err := repo.Create(ctx, state)
		assert.NoError(t, err)
		assert.False(t, state.CreatedAt.IsZero())
		assert.False(t, state.UpdatedAt.IsZero())
	})

	// Test FindByUserID
	t.Run("FindByUserID", func(t *testing.T) {
		found, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.Equal(t, userID, found.UserID)
		assert.NotEmpty(t, found.NotificationState.SentUnique)
	})

	// Test UpdateLastLogin
	t.Run("UpdateLastLogin", func(t *testing.T) {
		beforeUpdate := time.Now()
		err := repo.UpdateLastLogin(ctx, userID)
		assert.NoError(t, err)

		// Verify update
		updated, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.True(t, updated.LastLoginAt.After(beforeUpdate))
	})

	// Test UpdateLastExpenseLogged
	t.Run("UpdateLastExpenseLogged", func(t *testing.T) {
		beforeUpdate := time.Now()
		err := repo.UpdateLastExpenseLogged(ctx, userID)
		assert.NoError(t, err)

		// Verify update
		updated, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.True(t, updated.LastExpenseLoggedAt.After(beforeUpdate))
	})

	// Test UpdateLastProgressionActivity
	t.Run("UpdateLastProgressionActivity", func(t *testing.T) {
		beforeUpdate := time.Now()
		err := repo.UpdateLastProgressionActivity(ctx, userID)
		assert.NoError(t, err)

		// Verify update
		updated, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.True(t, updated.LastProgressionActivity.After(beforeUpdate))
	})

	// Test UpdateNotificationSent
	t.Run("UpdateNotificationSent", func(t *testing.T) {
		notificationID := "PRIMEIRO_SONHO"
		err := repo.UpdateNotificationSent(ctx, userID, notificationID)
		assert.NoError(t, err)

		// Verify update
		updated, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.Contains(t, updated.NotificationState.SentUnique, notificationID)
		assert.Equal(t, 2, updated.NotificationState.TotalSent)
	})

	// Test UpdateOnboardingEvent
	t.Run("UpdateOnboardingEvent", func(t *testing.T) {
		eventType := "STARTED_DIAGNOSIS"
		beforeUpdate := time.Now()
		err := repo.UpdateOnboardingEvent(ctx, userID, eventType)
		assert.NoError(t, err)

		// Verify update
		updated, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.True(t, updated.OnboardingState.StartedDiagnosisAt.After(beforeUpdate))
	})

	// Test FindByUserIDs
	t.Run("FindByUserIDs", func(t *testing.T) {
		userIDs := []string{userID, "non-existent-user"}
		states, err := repo.FindByUserIDs(ctx, userIDs)
		assert.NoError(t, err)
		assert.Len(t, states, 1) // Only one user exists
		assert.Equal(t, userID, states[0].UserID)
	})

	// Test Delete
	t.Run("Delete", func(t *testing.T) {
		err := repo.Delete(ctx, userID)
		assert.NoError(t, err)

		// Verify deletion
		_, err = repo.FindByUserID(ctx, userID)
		assert.Error(t, err) // Should not be found
	})
}

// TestUserActivityState_Upsert tests the upsert functionality
func TestUserActivityState_Upsert(t *testing.T) {
	// Skip if no MongoDB connection available
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup test database connection
	ctx := context.Background()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI("mongodb://localhost:27017"))
	require.NoError(t, err)
	defer client.Disconnect(ctx)

	// Use test database
	db := client.Database("dinbora_test_notifications_upsert")
	defer db.Drop(ctx) // Clean up after test

	// Create repository
	repo := New(db)

	userID := "test-user-upsert"
	state := &notification.UserActivityState{
		UserID:              userID,
		LastLoginAt:         time.Now(),
		LastExpenseLoggedAt: time.Now(),
	}

	// Test upsert (insert)
	t.Run("Upsert_Insert", func(t *testing.T) {
		err := repo.Upsert(ctx, state)
		assert.NoError(t, err)

		// Verify insertion
		found, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.Equal(t, userID, found.UserID)
	})

	// Test upsert (update)
	t.Run("Upsert_Update", func(t *testing.T) {
		state.LastProgressionActivity = time.Now()
		err := repo.Upsert(ctx, state)
		assert.NoError(t, err)

		// Verify update
		found, err := repo.FindByUserID(ctx, userID)
		assert.NoError(t, err)
		assert.False(t, found.LastProgressionActivity.IsZero())
	})
}
