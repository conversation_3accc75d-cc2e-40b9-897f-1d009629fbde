package repository

const (
	USERS_COLLECTION                       = "users"
	USERS_COLLECTION_TRASH                 = "users.trash"
	USERS_FINANCIAL_PROFILE_HISTORY        = "users.financial_profile_history"
	TRAILS_COLLECTION                      = "trails"
	TRAILS_EXTRA_COLLECTION                = "trails.extra"
	TRAILS_TUTORIAL_COLLECTION             = "trails.tutorial"
	PROGRESSIONS_COLLECTION                = "progressions"           // Deprecated should be removed after complete migration
	PROGRESSIONS_EVENTS                    = "progressions.events"    // Not in legacy
	PROGRESSIONS_SUMMARIES                 = "progressions.summaries" // Not in legacy
	ACHIEVEMENTS_COLLECTION                = "achievements"
	VAULTS_COLLECTION                      = "vaults"
	TICKERS_COLLECTION                     = "tickers"
	INVESTMENTS_CATEGORIES_COLLECTION      = "investments.categories"
	WALLETS_COLLECTION                     = "wallets"
	DREAMBOARDS_COLLECTION                 = "dreamboards"
	DREAMBOARDS_DREAMS_COLLECTION          = "dreamboards.dreams"     // New: separate dreams collection
	DREAMBOARDS_CATEGORIES_COLLECTION      = "dreamboards.categories" // New: separate categories collection
	DREAMBOARDS_COLLECTION_TRASH           = "dreamboards.trash"
	DREAMBOARDS_CONTRIBUTIONS_COLLECTION   = "dreamboards.contributions"
	FINANCIAL_SHEETS_COLLECTION            = "financial_sheets"
	FINANCIAL_SHEETS_COLLECTION_TRASH      = "financial_sheets.trash"
	FINANCIAL_SHEETS_COLLECTION_CATEGORIES = "financial_sheets.categories"
	FINANCIAL_DNA_COLLECTION               = "financial_dna"
	FINANCIAL_DNA_COLLECTION_TRASH         = "financial_dna.trash"
	LEAGUES_COLLECTION                     = "leagues"
	SHARELINKS_COLLECTION                  = "sharelinks"

	// Billing Collections
	BILLING_PLANS_COLLECTION         = "billing.plans"
	BILLING_SUBSCRIPTIONS_COLLECTION = "billing.subscriptions"
	BILLING_PAYMENTS_COLLECTION      = "billing.payments"

	// Dashboard Collections
	DASHBOARDS_FINANCIAL_MAP_COLLECTION          = "dashboards.financialmap"
	DASHBOARDS_FINANCIAL_INDEPENDENCE_COLLECTION = "dashboards.financialindependence"

	// Gamification Collections
	GAMIFICATION_USER_ACHIEVEMENTS_COLLECTION = "gamification.userachievements"

	// Firebase Cloud Messaging Collection
	FCM_TOKENS_COLLECTION = "fcm_tokens"

	// Notification Collections
	NOTIFICATIONS_USER_ACTIVITY_STATES_COLLECTION = "notifications.user_activity_states"

	// Auth Collections
	AUTH_SESSIONS_COLLECTION = "auth.sessions"
	AUTH_OTPS_COLLECTION     = "auth.otps"

	CHANGELOGS_COLLECTION = "changelogs"
)
