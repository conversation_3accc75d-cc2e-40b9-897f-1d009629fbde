package notification

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockNotificationService mocks the notification service
type MockNotificationService struct {
	mock.Mock
	PushNotifier MockPushNotifier
}

// MockPushNotifier mocks the push notifier
type MockPushNotifier struct {
	mock.Mock
}

func (m *MockPushNotifier) SendPushNotification(ctx context.Context, userID, title, message string, data map[string]string) error {
	args := m.Called(ctx, userID, title, message, data)
	return args.Error(0)
}

func (m *MockPushNotifier) ProcessScheduledNotifications(ctx context.Context, notificationType string) error {
	args := m.Called(ctx, notificationType)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastLogin(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastExpenseLogged(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockPushNotifier) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	args := m.Called(ctx, userID, eventType)
	return args.Error(0)
}

// TestProcessScheduledNotifications tests the scheduled notification processing endpoint
type dummyValidator struct{}

func (v *dummyValidator) Validate(i interface{}) error {
	return nil
}

type NoopPushNotifier struct{}

func (n *NoopPushNotifier) SendPushNotification(ctx context.Context, userID, title, message string, data map[string]string) error {
	return nil
}
func (n *NoopPushNotifier) ProcessScheduledNotifications(ctx context.Context, notificationType string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastLogin(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastExpenseLogged(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	return nil
}
func (n *NoopPushNotifier) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	return nil
}

func TestProcessScheduledNotifications(t *testing.T) {
	e := echo.New()
	e.Validator = &dummyValidator{}
	mockPushNotifier := new(MockPushNotifier)
	noopPushNotifier := &NoopPushNotifier{}
	notificationService := &notification.Service{PushNotifier: mockPushNotifier}
	ctrl := New(notificationService)

	testCases := []struct {
		name                 string
		body                 ProcessNotificationsRequestDTO
		mockSetup            func()
		expectedStatus       int
		expectedResponse     interface{}
		expectError          bool
		expectedErrorMessage string
	}{
		{
			name: "ValidRequest - unique",
			body: ProcessNotificationsRequestDTO{Type: "unique"},
			mockSetup: func() {
				// Do not set mock expectations for goroutine calls in tests
			},
			expectedStatus: http.StatusAccepted,
			expectedResponse: ProcessNotificationsResponseDTO{
				Status:  "accepted",
				Message: "Notification processing started",
				Type:    "unique",
			},
		},
		{
			name: "ValidRequest - recurrent",
			body: ProcessNotificationsRequestDTO{Type: "recurrent"},
			mockSetup: func() {
				// Do not set mock expectations for goroutine calls in tests
			},
			expectedStatus: http.StatusAccepted,
			expectedResponse: ProcessNotificationsResponseDTO{
				Status:  "accepted",
				Message: "Notification processing started",
				Type:    "recurrent",
			},
		},
		{
			name:           "InvalidType",
			body:           ProcessNotificationsRequestDTO{Type: "invalid"},
			mockSetup:      func() {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "InvalidRequestBody",
			body:           ProcessNotificationsRequestDTO{},
			mockSetup:      func() {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "ServiceUnavailable",
			body: ProcessNotificationsRequestDTO{Type: "unique"},
			mockSetup: func() {
				// Detach the notifier for this test case
				ctrl.(*controller).NotificationService.PushNotifier = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectError:    true,
		},
		{
			name: "ErrorProcessing",
			body: ProcessNotificationsRequestDTO{Type: "unique"},
			mockSetup: func() {
				mockPushNotifier.On("ProcessScheduledNotifications", mock.Anything, "unique").Return(assert.AnError).Once()
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset the notifier before each test
			if tc.name == "ValidRequest - unique" || tc.name == "ValidRequest - recurrent" {
				ctrl.(*controller).NotificationService.PushNotifier = noopPushNotifier
			} else {
				ctrl.(*controller).NotificationService.PushNotifier = mockPushNotifier
			}
			mockPushNotifier.Mock = mock.Mock{} // Reset mock expectations

			tc.mockSetup()

			jsonBody, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/notifications/process", bytes.NewReader(jsonBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			handler := ctrl.ProcessScheduledNotifications()
			err := handler(c)

			if tc.expectError {
				assert.Equal(t, tc.expectedStatus, rec.Code)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedStatus, rec.Code)
				if tc.expectedResponse != nil {
					var response ProcessNotificationsResponseDTO
					err = json.Unmarshal(rec.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Equal(t, tc.expectedResponse, response)
				}
			}

			mockPushNotifier.AssertExpectations(t)
		})
	}
}

// TestSendTestNotification tests the test notification endpoint
func TestSendTestNotification(t *testing.T) {
	e := echo.New()
	e.Validator = &dummyValidator{}
	mockPushNotifier := new(MockPushNotifier)
	notificationService := &notification.Service{PushNotifier: mockPushNotifier}
	ctrl := New(notificationService)

	testCases := []struct {
		name             string
		body             interface{}
		mockSetup        func()
		expectedStatus   int
		expectedResponse interface{}
		expectError      bool
	}{
		{
			name: "ValidRequest",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				mockPushNotifier.On("SendPushNotification", mock.Anything, "test-user-123", "Test Notification", "This is a test message", mock.Anything).Return(nil).Once()
			},
			expectedStatus: http.StatusOK,
			expectedResponse: SendTestNotificationResponseDTO{
				Status:  "sent",
				Message: "Test notification sent successfully",
				UserID:  "test-user-123",
			},
		},
		{
			name: "MissingUserID",
			body: SendTestNotificationRequestDTO{
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup:      func() {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "ServiceUnavailable",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				// Detach the notifier for this test case
				ctrl.(*controller).NotificationService.PushNotifier = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectError:    true,
		},
		{
			name: "ErrorSending",
			body: SendTestNotificationRequestDTO{
				UserID:  "test-user-123",
				Title:   "Test Notification",
				Message: "This is a test message",
			},
			mockSetup: func() {
				mockPushNotifier.On("SendPushNotification", mock.Anything, "test-user-123", "Test Notification", "This is a test message", mock.Anything).Return(assert.AnError).Once()
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.name == "MissingUserID" {
				ctrl.(*controller).NotificationService.PushNotifier = &NoopPushNotifier{}
			} else {
				ctrl.(*controller).NotificationService.PushNotifier = mockPushNotifier
			}
			mockPushNotifier.Mock = mock.Mock{} // Reset mock expectations

			tc.mockSetup()

			jsonBody, _ := json.Marshal(tc.body)
			req := httptest.NewRequest(http.MethodPost, "/notifications/test", bytes.NewReader(jsonBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			handler := ctrl.SendTestNotification()
			err := handler(c)

			if tc.expectError {
				assert.Equal(t, tc.expectedStatus, rec.Code)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedStatus, rec.Code)
				if tc.expectedResponse != nil {
					var response SendTestNotificationResponseDTO
					err = json.Unmarshal(rec.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Equal(t, tc.expectedResponse, response)
				}
			}

			mockPushNotifier.AssertExpectations(t)
		})
	}
}
