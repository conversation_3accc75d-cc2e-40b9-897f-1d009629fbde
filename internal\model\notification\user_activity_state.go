package notification

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserActivityState tracks user engagement data for push notification eligibility
type UserActivityState struct {
	ObjectID                primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                      string             `json:"id,omitempty" bson:"-"`
	UserID                  string             `json:"userId" bson:"userId"`
	LastLoginAt             time.Time          `json:"lastLoginAt" bson:"lastLoginAt"`
	LastExpenseLoggedAt     time.Time          `json:"lastExpenseLoggedAt" bson:"lastExpenseLoggedAt"`
	LastProgressionActivity time.Time          `json:"lastProgressionActivity" bson:"lastProgressionActivity"`
	OnboardingState         OnboardingState    `json:"onboarding" bson:"onboarding"`
	NotificationState       NotificationState  `json:"notifications" bson:"notifications"`
	CreatedAt               time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt               time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// OnboardingState tracks user onboarding progress
type OnboardingState struct {
	StartedDiagnosisAt    time.Time `json:"startedDiagnosisAt" bson:"startedDiagnosisAt"`
	CompletedDiagnosisAt  time.Time `json:"completedDiagnosisAt" bson:"completedDiagnosisAt"`
	CreatedFirstDreamAt   time.Time `json:"createdFirstDreamAt" bson:"createdFirstDreamAt"`
	CreatedFirstBudgetAt  time.Time `json:"createdFirstBudgetAt" bson:"createdFirstBudgetAt"`
	CompletedFirstTrailAt time.Time `json:"completedFirstTrailAt" bson:"completedFirstTrailAt"`
}

// NotificationState tracks sent notifications to prevent duplicates
type NotificationState struct {
	SentUnique map[string]time.Time `json:"sentUnique" bson:"sentUnique"`
	LastSentAt time.Time            `json:"lastSentAt" bson:"lastSentAt"`
	TotalSent  int                  `json:"totalSent" bson:"totalSent"`
}

// PrepareCreate prepares the activity state for creation
func (uas *UserActivityState) PrepareCreate() {
	uas.CreatedAt = time.Now()
	uas.UpdatedAt = uas.CreatedAt

	if uas.NotificationState.SentUnique == nil {
		uas.NotificationState.SentUnique = make(map[string]time.Time)
	}
}

// PrepareUpdate prepares the activity state for update
func (uas *UserActivityState) PrepareUpdate() {
	uas.UpdatedAt = time.Now()
}

// Sanitize removes sensitive information and sets ID
func (uas *UserActivityState) Sanitize() *UserActivityState {
	if !uas.ObjectID.IsZero() {
		uas.ID = uas.ObjectID.Hex()
	}
	return uas
}
