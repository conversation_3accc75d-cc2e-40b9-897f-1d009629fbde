// File: internal/notification/rules.go
package notification

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
)

// NotificationRule defines a push notification rule
type NotificationRule struct {
	ID         string
	Title      string // Added Title for consistency, you can adjust as needed.
	Message    string
	Priority   int // Lower number = higher priority
	IsEligible func(user *model.User, state *notification.UserActivityState, startOfToday time.Time) bool
}

// getNotificationRules returns rules based on notification type
func (f *FirebasePushNotifier) getNotificationRules(notificationType string) []NotificationRule {
	switch notificationType {
	case "unique":
		return f.getUniqueRules()
	case "recurrent":
		return f.getRecurrentRules()
	default:
		return []NotificationRule{}
	}
}

// getUniqueRules returns one-time notification rules based on the spreadsheet
func (f *FirebasePushNotifier) getUniqueRules() []NotificationRule {
	return []NotificationRule{
		{
			ID:       "DIAGNOSTICO_FINANCEIRO",
			Title:    "Seu diagnóstico te espera!",
			Message:  "Ei, seu diagnóstico tá esperando! Bora descobrir seu perfil financeiro? 🚀",
			Priority: 1,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentUnique["DIAGNOSTICO_FINANCEIRO"]; sent {
					return false
				}
				// Trigger: "Não concluiu o diagnóstico", 1 day after starting
				return state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
					!state.OnboardingState.StartedDiagnosisAt.IsZero() &&
					time.Since(state.OnboardingState.StartedDiagnosisAt) >= 24*time.Hour
			},
		},
		{
			ID:       "SONHAR",
			Title:    "Bora sonhar alto?",
			Message:  "Agora que você se conhece, bora sonhar alto? Registre seus sonhos! 🌈",
			Priority: 2,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentUnique["SONHAR"]; sent {
					return false
				}
				// Trigger: "Finalizou o diagnóstico mas não criou sonhos", 1 day after finishing diagnosis
				return !state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
					state.OnboardingState.CreatedFirstDreamAt.IsZero() &&
					time.Since(state.OnboardingState.CompletedDiagnosisAt) >= 24*time.Hour
			},
		},
		{
			ID:       "ORCAMENTO",
			Title:    "Organize a casa!",
			Message:  "Agora é hora de organizar a casa! Faz seu orçamento e bora realizar! 💪",
			Priority: 3,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if _, sent := state.NotificationState.SentUnique["ORCAMENTO"]; sent {
					return false
				}
				// Trigger: "Criou sonho, mas não montou orçamento", 1 day after creating first goal
				return !state.OnboardingState.CreatedFirstDreamAt.IsZero() &&
					state.OnboardingState.CreatedFirstBudgetAt.IsZero() &&
					time.Since(state.OnboardingState.CreatedFirstDreamAt) >= 24*time.Hour
			},
		},
	}
}

// getRecurrentRules returns recurring notification rules based on the spreadsheet
func (f *FirebasePushNotifier) getRecurrentRules() []NotificationRule {
	return []NotificationRule{
		{
			ID:       "HABITO_MENSAL",
			Title:    "Novo mês, nova chance!",
			Message:  "Novo mês, nova chance de brilhar! Já fez seu orçamento? ☑️",
			Priority: 1,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				// Trigger: "Início de um novo mês", on the 1st business day
				return isFirstBusinessDayOfMonth()
			},
		},
		{
			ID:       "INATIVIDADE",
			Title:    "Sentimos sua falta!",
			Message:  "Sentimos sua falta! Seus sonhos ainda tão aqui esperando você voltar! ✨",
			Priority: 2,
			IsEligible: func(user *model.User, state *notification.UserActivityState, _ time.Time) bool {
				if state.LastLoginAt.IsZero() {
					return false
				}
				// Trigger: "Usuário não acessa o app", "A cada 7 dias"
				return time.Since(state.LastLoginAt) >= 7*24*time.Hour
			},
		},
		{
			ID:       "APONTAMENTO_DIARIO",
			Title:    "Não esqueça de anotar!",
			Message:  "Veja variações abaixo", // This is a placeholder, it will be replaced by findBestNotificationRule
			Priority: 3,
			IsEligible: func(user *model.User, state *notification.UserActivityState, startOfToday time.Time) bool {
				// Trigger: "Manter sequência de registro de gastos", if not logged in over 24h
				// We check for IsZero to avoid triggering for new users who never logged an expense.
				if state.LastExpenseLoggedAt.IsZero() {
					return false
				}
				return state.LastExpenseLoggedAt.Before(startOfToday)
			},
		},
	}
}

// findBestNotificationRule finds the highest priority eligible rule
func (f *FirebasePushNotifier) findBestNotificationRule(user *model.User, state *notification.UserActivityState, rules []NotificationRule, startOfToday time.Time) *NotificationRule {
	var bestRule *NotificationRule

	// We iterate through a copy to avoid modifying the original rule slice
	for i := range rules {
		rule := rules[i] // Make a copy to avoid pointer issues in loops
		if rule.IsEligible(user, state, startOfToday) {
			if bestRule == nil || rule.Priority < bestRule.Priority {
				bestRule = &rule
			}
		}
	}

	// Special handling for APONTAMENTO_DIARIO to set the correct daily message
	if bestRule != nil && bestRule.ID == "APONTAMENTO_DIARIO" {
		bestRule.Message = getDailyMessage()
	}

	return bestRule
}

// --- HELPER FUNCTIONS ---

// getDailyMessage returns a specific message based on the day of the week.
func getDailyMessage() string {
	switch time.Now().Weekday() {
	case time.Monday:
		return "Vai quebrar a sequência? Corre e anota seus gastos!💸"
	case time.Tuesday:
		return "Tá esquecendo de mim? Seus gastos de hoje tão no vácuo! 😉"
	case time.Wednesday:
		return "Só 1 min! Anota aí antes que o dia vire lenda 💰"
	case time.Thursday:
		return "Você promete que não vai me ignorar de novo, né? 👀"
	case time.Friday:
		return "O futuro te vê... não anotando os gastos. Bora mudar isso? 🔮"
	case time.Saturday:
		return "Tá com vergonha de anotar o que gastou no iFood? 😂 Bora lá!"
	case time.Sunday:
		return "Sequência linda a sua... seria uma pena se quebrasse 💔"
	default:
		return "Não se esqueça de anotar seus gastos de hoje!"
	}
}

// isFirstBusinessDayOfMonth checks if the current day is the first weekday of the current month.
func isFirstBusinessDayOfMonth() bool {
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()

	// Check if today is a weekend, if so, it can't be the first business day.
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		return false
	}

	// Find the first day of the month
	firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, now.Location())

	// Iterate from the 1st of the month until we find the first weekday
	for day := 0; day < 7; day++ {
		dateToCheck := firstOfMonth.AddDate(0, 0, day)
		weekday := dateToCheck.Weekday()

		if weekday != time.Saturday && weekday != time.Sunday {
			// We found the first business day of the month.
			// Now, check if today is that day.
			return dateToCheck.Day() == now.Day()
		}
	}
	return false // Should not be reached
}
