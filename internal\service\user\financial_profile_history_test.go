package user

import (
	"context"
	"mime/multipart"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockRepository for testing
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) CreateFinancialProfileHistory(ctx context.Context, history *model.FinancialProfileHistory) error {
	args := m.Called(ctx, history)
	return args.Error(0)
}

// Add other required methods to satisfy the interface
func (m *MockRepository) Find(ctx context.Context, id primitive.ObjectID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockRepository) FindAll(ctx context.Context) ([]*model.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockRepository) FindAdmins(ctx context.Context) ([]*model.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockRepository) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	args := m.Called(ctx, phone)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockRepository) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	args := m.Called(ctx, referralCode)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockRepository) FindByReferringUserID(ctx context.Context, referringUserID string) ([]*model.User, error) {
	args := m.Called(ctx, referringUserID)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockRepository) FindWithFilter(ctx context.Context, filter interface{}) ([]*model.User, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockRepository) FindUsersWithFCMTokens(ctx context.Context) ([]*model.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockRepository) FindFinancialProfileHistory(ctx context.Context, userID string) ([]*model.FinancialProfileHistory, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*model.FinancialProfileHistory), args.Error(1)
}

func (m *MockRepository) FindFinancialProfileHistoryByUsers(ctx context.Context, userIDs []string) ([]*model.FinancialProfileHistory, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]*model.FinancialProfileHistory), args.Error(1)
}

func (m *MockRepository) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockRepository) Create(ctx context.Context, user *model.User) (string, error) {
	args := m.Called(ctx, user)
	return args.String(0), args.Error(1)
}

func (m *MockRepository) CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error {
	args := m.Called(ctx, deletedUser)
	return args.Error(0)
}

func (m *MockRepository) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MockProgressionService for testing
type MockProgressionService struct {
	mock.Mock
}

func (m *MockProgressionService) RecordProgress(ctx context.Context, userId string, body *progression.ProgressionBody) error {
	args := m.Called(ctx, userId, body)
	return args.Error(0)
}

func (m *MockProgressionService) GetUserProgress(ctx context.Context, userId string) (*progression.ProgressSummary, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).(*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionService) GetUsersProgress(ctx context.Context, userIDs []string) ([]*progression.ProgressSummary, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionService) GetTrailProgress(ctx context.Context, userID, userClassification string, trailID string) (*progression.TrailSummary, error) {
	args := m.Called(ctx, userID, userClassification, trailID)
	return args.Get(0).(*progression.TrailSummary), args.Error(1)
}

func (m *MockProgressionService) CheckUserPermission(ctx context.Context, userClassification string, trailId string) error {
	args := m.Called(ctx, userClassification, trailId)
	return args.Error(0)
}

func (m *MockProgressionService) Initialize(ctx context.Context, userId string) error {
	args := m.Called(ctx, userId)
	return args.Error(0)
}

func (m *MockProgressionService) Find(ctx context.Context, id string) (*progression.Progression, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionService) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionService) FindProgressionsByUsers(ctx context.Context, userIds []string) (map[string]*progression.Progression, error) {
	args := m.Called(ctx, userIds)
	return args.Get(0).(map[string]*progression.Progression), args.Error(1)
}

func (m *MockProgressionService) Update(ctx context.Context, progression *progression.Progression) error {
	args := m.Called(ctx, progression)
	return args.Error(0)
}

func (m *MockProgressionService) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// Updated method signature to match interface: FindTrail -> TrailSummary
func (m *MockProgressionService) FindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.TrailSummary, error) {
	args := m.Called(ctx, userId, userClassification, trailId)
	return args.Get(0).(*progression.TrailSummary), args.Error(1)
}

func (m *MockProgressionService) CreateChallenge(ctx context.Context, userId string, userClassification string, progression *progression.ProgressionBody) error {
	args := m.Called(ctx, userId, userClassification, progression)
	return args.Error(0)
}

// Updated method signature to match interface: FindChallenge -> ChallengeProgress
func (m *MockProgressionService) FindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.ChallengeProgress, error) {
	args := m.Called(ctx, userId, userClassification, trailId, challengeId)
	return args.Get(0).(*progression.ChallengeProgress), args.Error(1)
}

// Updated method signature to match interface: FindChallengePhase -> PhaseProgress
func (m *MockProgressionService) FindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.PhaseProgress, error) {
	args := m.Called(ctx, userId, userClassification, trailId, challengeId, challengePhase)
	return args.Get(0).(*progression.PhaseProgress), args.Error(1)
}

func (m *MockProgressionService) FindChallengePhaseCurrentPoints(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, phaseId string) (int, error) {
	args := m.Called(ctx, userId, userClassification, trailId, challengeId, phaseId)
	return args.Int(0), args.Error(1)
}

func (m *MockProgressionService) FindTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error) {
	args := m.Called(ctx, userId, userClassification, trailId)
	return args.Get(0).([]*progression.TrailCard), args.Error(1)
}

func (m *MockProgressionService) FindRegularTrailCards(ctx context.Context, userId string, trailId string) ([]*progression.TrailCard, error) {
	args := m.Called(ctx, userId, trailId)
	return args.Get(0).([]*progression.TrailCard), args.Error(1)
}

func (m *MockProgressionService) FindExtraTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error) {
	args := m.Called(ctx, userId, userClassification, trailId)
	return args.Get(0).([]*progression.TrailCard), args.Error(1)
}

func (m *MockProgressionService) FindModuleCards(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) ([]*progression.LessonCard, *progression.ChallengeCard, error) {
	args := m.Called(ctx, userId, userClassification, trailId, lessonId)
	return args.Get(0).([]*progression.LessonCard), args.Get(1).(*progression.ChallengeCard), args.Error(2)
}

func (m *MockProgressionService) FindChallengePhaseCards(ctx context.Context, userId string, userClassification string, trailId string, phaseId string) ([]*progression.ChallengePhaseCard, error) {
	args := m.Called(ctx, userId, userClassification, trailId, phaseId)
	return args.Get(0).([]*progression.ChallengePhaseCard), args.Error(1)
}

func (m *MockProgressionService) CreateLesson(ctx context.Context, userId string, userClassification string, progression *progression.ProgressionBody) error {
	args := m.Called(ctx, userId, userClassification, progression)
	return args.Error(0)
}

// Updated method signature to match interface: FindLesson -> LessonProgress
func (m *MockProgressionService) FindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.LessonProgress, error) {
	args := m.Called(ctx, userId, userClassification, trailId, lessonId)
	return args.Get(0).(*progression.LessonProgress), args.Error(1)
}

func (m *MockProgressionService) LegacyCreateLessonChallenge(ctx context.Context, userId string, userClassification string, progression *progression.ProgressionBody) error {
	args := m.Called(ctx, userId, userClassification, progression)
	return args.Error(0)
}

// Legacy Methods (newly added)
func (m *MockProgressionService) LegacyFindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.Trail, error) {
	args := m.Called(ctx, userId, userClassification, trailId)
	return args.Get(0).(*progression.Trail), args.Error(1)
}

func (m *MockProgressionService) LegacyFindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.Lesson, error) {
	args := m.Called(ctx, userId, userClassification, trailId, lessonId)
	return args.Get(0).(*progression.Lesson), args.Error(1)
}

func (m *MockProgressionService) LegacyFindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.Challenge, error) {
	args := m.Called(ctx, userId, userClassification, trailId, challengeId)
	return args.Get(0).(*progression.Challenge), args.Error(1)
}

func (m *MockProgressionService) LegacyFindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error) {
	args := m.Called(ctx, userId, userClassification, trailId, challengeId, challengePhase)
	return args.Get(0).(*progression.ChallengePhase), args.Error(1)
}

func (m *MockProgressionService) GetUserEvents(ctx context.Context, userId string, limit int) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

// MockFinancialDNAService for testing
type MockFinancialDNAService struct {
	mock.Mock
}

func (m *MockFinancialDNAService) Create(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) Find(ctx context.Context, id string) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) FindByUser(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) Update(ctx context.Context, tree *financialdna.FinancialDNATree) error {
	args := m.Called(ctx, tree)
	return args.Error(0)
}

func (m *MockFinancialDNAService) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockFinancialDNAService) PatchMember(ctx context.Context, userID, memberID string, member *financialdna.FamilyMember, photo *multipart.FileHeader) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID, memberID, member, photo)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) DeleteMember(ctx context.Context, userID, memberID string) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID, memberID)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) CreateChild(ctx context.Context, userID, parentID string, child *financialdna.FamilyMember) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID, parentID, child)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) DeleteChild(ctx context.Context, userID, parentID, childID string) (*financialdna.FinancialDNATree, error) {
	args := m.Called(ctx, userID, parentID, childID)
	return args.Get(0).(*financialdna.FinancialDNATree), args.Error(1)
}

func (m *MockFinancialDNAService) Initialize(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func TestUpdateFinancialProfile_CreatesHistoryForNewProfile(t *testing.T) {
	// Setup
	mockRepo := &MockRepository{}
	mockProgressionService := &MockProgressionService{}
	mockFinancialDNAService := &MockFinancialDNAService{}
	service := &service{
		Repository:          mockRepo,
		ProgressionService:  mockProgressionService,
		FinancialDNAService: mockFinancialDNAService,
	}

	ctx := context.Background()
	userID := primitive.NewObjectID()

	// Mock user without financial profile
	user := &model.User{
		ID:               userID.Hex(),
		Email:            "<EMAIL>", // Add required email field
		Name:             "Test User",        // Add required name field
		FinancialProfile: nil,
		Classification:   "standard", // Add classification for the progression service call
	}

	// Mock expectations
	mockRepo.On("Find", ctx, userID).Return(user, nil)
	mockRepo.On("CreateFinancialProfileHistory", ctx, mock.AnythingOfType("*model.FinancialProfileHistory")).Return(nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*model.User")).Return(nil)

	// Mock progression service to return points that classify as "Balanced" (e.g., 50 points)
	mockProgressionService.On("FindChallengePhaseCurrentPoints", ctx, userID.Hex(), "standard", "67f6ddf3181babca8896e73c", "dna-financeiro", "perfil-financeiro").Return(50, nil)

	// Mock financial DNA service to return a tree with "me" as undefined
	mockFinancialDNAService.On("FindByUser", ctx, userID.Hex()).Return(&financialdna.FinancialDNATree{
		Members: []*financialdna.FamilyMember{
			{
				ID:              "me",
				Name:            "Me",
				FinancialStatus: financialdna.FinancialStatusUndefined,
			},
		},
	}, nil)
	mockFinancialDNAService.On("Update", ctx, mock.AnythingOfType("*financialdna.FinancialDNATree")).Return(nil)

	// Execute
	err := service.UpdateFinancialProfile(ctx, userID.Hex())

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockProgressionService.AssertExpectations(t)
	mockFinancialDNAService.AssertExpectations(t)
}

func TestGetLatestStatusForMonth(t *testing.T) {
	service := &service{}

	// Use a fixed date to avoid month boundary issues
	baseDate := time.Date(2024, 6, 15, 12, 0, 0, 0, time.UTC)
	histories := []*model.FinancialProfileHistory{
		{
			UserID:    "user1",
			Status:    model.StatusIndebted,
			CreatedAt: baseDate.AddDate(0, -3, 0), // March 15, 2024
		},
		{
			UserID:    "user1",
			Status:    model.StatusBalanced,
			CreatedAt: baseDate.AddDate(0, -2, 0), // April 15, 2024
		},
		{
			UserID:    "user1",
			Status:    model.StatusInvestor,
			CreatedAt: baseDate.AddDate(0, -1, 0), // May 15, 2024
		},
		{
			UserID:    "user2",
			Status:    model.StatusOverindebted,
			CreatedAt: baseDate.AddDate(0, -2, 0), // April 15, 2024
		},
	}

	// Test getting status for user1 at current month (June 2024 - should be Investor)
	endOfCurrentMonth := time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
	status := service.getLatestStatusForMonth(histories, "user1", endOfCurrentMonth)
	assert.Equal(t, "Investor", status)

	// Test getting status for user1 at end of April 2024 (should be Balanced)
	endOfApril := time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
	status = service.getLatestStatusForMonth(histories, "user1", endOfApril)
	assert.Equal(t, "Balanced", status)

	// Test getting status for user1 at end of February 2024 (should be empty - no history yet)
	endOfFebruary := time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
	status = service.getLatestStatusForMonth(histories, "user1", endOfFebruary)
	assert.Equal(t, "", status)

	// Test getting status for user2
	status = service.getLatestStatusForMonth(histories, "user2", endOfCurrentMonth)
	assert.Equal(t, "Overindebted", status)

	// Test getting status for non-existent user
	status = service.getLatestStatusForMonth(histories, "user3", endOfCurrentMonth)
	assert.Equal(t, "", status)
}

func TestFinancialProfileHistory_String(t *testing.T) {
	tests := []struct {
		status   model.FinancialProfileStatus
		expected string
	}{
		{model.StatusUndefined, "Undefined"},
		{model.StatusOverindebted, "Overindebted"},
		{model.StatusIndebted, "Indebted"},
		{model.StatusBalanced, "Balanced"},
		{model.StatusInvestor, "Investor"},
	}

	for _, test := range tests {
		history := &model.FinancialProfileHistory{Status: test.status}
		assert.Equal(t, test.expected, history.String())
	}
}
