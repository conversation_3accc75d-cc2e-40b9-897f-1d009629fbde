package notification

import "time"

// ProcessNotificationsRequestDTO represents the request for processing scheduled notifications
type ProcessNotificationsRequestDTO struct {
	Type string `json:"type" validate:"required,oneof=unique recurrent"`
}

// ProcessNotificationsResponseDTO represents the response for processing scheduled notifications
type ProcessNotificationsResponseDTO struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Type    string `json:"type"`
}

// SendTestNotificationRequestDTO represents the request for sending a test notification
type SendTestNotificationRequestDTO struct {
	UserID  string `json:"userId" validate:"required"`
	Title   string `json:"title" validate:"required,max=100"`
	Message string `json:"message" validate:"required,max=500"`
}

// SendTestNotificationResponseDTO represents the response for sending a test notification
type SendTestNotificationResponseDTO struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	UserID  string `json:"userId"`
}

// GetUserActivityStateResponseDTO represents the response for getting user activity state
type GetUserActivityStateResponseDTO struct {
	UserID                  string                         `json:"userId"`
	LastLoginAt             time.Time                      `json:"lastLoginAt"`
	LastExpenseLoggedAt     time.Time                      `json:"lastExpenseLoggedAt"`
	LastProgressionActivity time.Time                      `json:"lastProgressionActivity"`
	OnboardingState         OnboardingStateDTO             `json:"onboarding"`
	NotificationState       NotificationStateDTO           `json:"notifications"`
	CreatedAt               time.Time                      `json:"createdAt"`
	UpdatedAt               time.Time                      `json:"updatedAt"`
}

// OnboardingStateDTO represents the onboarding state in DTOs
type OnboardingStateDTO struct {
	StartedDiagnosisAt      time.Time `json:"startedDiagnosisAt"`
	CompletedDiagnosisAt    time.Time `json:"completedDiagnosisAt"`
	CreatedFirstGoalAt      time.Time `json:"createdFirstGoalAt"`
	CreatedFirstBudgetAt    time.Time `json:"createdFirstBudgetAt"`
	CompletedFirstTrailAt   time.Time `json:"completedFirstTrailAt"`
}

// NotificationStateDTO represents the notification state in DTOs
type NotificationStateDTO struct {
	SentUnique map[string]time.Time `json:"sentUnique"`
	LastSentAt time.Time            `json:"lastSentAt"`
	TotalSent  int                  `json:"totalSent"`
}
