package notification

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/notification"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockActivityStateRepository mocks the notification repository
type MockActivityStateRepository struct {
	mock.Mock
}

func (m *MockActivityStateRepository) FindByUserID(ctx context.Context, userID string) (*notification.UserActivityState, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*notification.UserActivityState), args.Error(1)
}

func (m *MockActivityStateRepository) FindByUserIDs(ctx context.Context, userIDs []string) ([]*notification.UserActivityState, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).([]*notification.UserActivityState), args.Error(1)
}

func (m *MockActivityStateRepository) FindEligibleUsers(ctx context.Context, criteria map[string]interface{}) ([]*notification.UserActivityState, error) {
	args := m.Called(ctx, criteria)
	return args.Get(0).([]*notification.UserActivityState), args.Error(1)
}

func (m *MockActivityStateRepository) Create(ctx context.Context, state *notification.UserActivityState) error {
	args := m.Called(ctx, state)
	return args.Error(0)
}

func (m *MockActivityStateRepository) Update(ctx context.Context, state *notification.UserActivityState) error {
	args := m.Called(ctx, state)
	return args.Error(0)
}

func (m *MockActivityStateRepository) UpdateLastLogin(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityStateRepository) UpdateLastExpenseLogged(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityStateRepository) UpdateLastProgressionActivity(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityStateRepository) UpdateNotificationSent(ctx context.Context, userID, notificationID string) error {
	args := m.Called(ctx, userID, notificationID)
	return args.Error(0)
}

func (m *MockActivityStateRepository) UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error {
	args := m.Called(ctx, userID, eventType)
	return args.Error(0)
}

func (m *MockActivityStateRepository) Upsert(ctx context.Context, state *notification.UserActivityState) error {
	args := m.Called(ctx, state)
	return args.Error(0)
}

func (m *MockActivityStateRepository) Delete(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// MockUserRepository mocks the user repository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) FindUsersWithFCMTokens(ctx context.Context) ([]*model.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.User), args.Error(1)
}

// Add other required methods as no-ops for interface compliance
func (m *MockUserRepository) Find(ctx context.Context, id primitive.ObjectID) (*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) Create(ctx context.Context, user *model.User) (string, error) {
	return "", nil
}

func (m *MockUserRepository) CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error {
	return nil
}

func (m *MockUserRepository) CreateFinancialProfileHistory(ctx context.Context, history *model.FinancialProfileHistory) error {
	return nil
}

func (m *MockUserRepository) Update(ctx context.Context, user *model.User) error {
	return nil
}

func (m *MockUserRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	return nil
}

func (m *MockUserRepository) FindAll(ctx context.Context) ([]*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindByReferringUserID(ctx context.Context, referringUserId string) ([]*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindWithFilter(ctx context.Context, filter interface{}) ([]*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) CountUsersByRole(ctx context.Context, role string) (int64, error) {
	return 0, nil
}

func (m *MockUserRepository) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	return nil, nil
}

func (m *MockUserRepository) FindFinancialProfileHistory(ctx context.Context, userID string) ([]*model.FinancialProfileHistory, error) {
	return nil, nil
}

func (m *MockUserRepository) FindFinancialProfileHistoryByUsers(ctx context.Context, userIDs []string) ([]*model.FinancialProfileHistory, error) {
	return nil, nil
}

func (m *MockUserRepository) FindAdmins(ctx context.Context) ([]*model.User, error) {
	return nil, nil
}

// TestNotificationRules tests the notification rule engine
func TestNotificationRules(t *testing.T) {
	// Create mock repositories
	mockActivityRepo := &MockActivityStateRepository{}
	mockUserRepo := &MockUserRepository{}

	// Create Firebase push notifier (without actual Firebase app for testing)
	notifier := &FirebasePushNotifier{
		firebaseApp:       nil, // Mock would be needed for full integration test
		firebaseService:   nil, // Mock would be needed for full integration test
		activityStateRepo: mockActivityRepo,
		userRepo:          mockUserRepo,
	}

	// Test unique rules
	t.Run("UniqueRules", func(t *testing.T) {
		rules := notifier.getUniqueRules()
		assert.NotEmpty(t, rules)

		// Check that we have expected rules
		ruleIDs := make([]string, len(rules))
		for i, rule := range rules {
			ruleIDs[i] = rule.ID
		}

		expectedRules := []string{
			"DIAGNOSTICO_FINANCEIRO",
			"SONHAR",
			"ORCAMENTO",
		}

		for _, expectedRule := range expectedRules {
			assert.Contains(t, ruleIDs, expectedRule)
		}
	})

	// Test recurrent rules
	t.Run("RecurrentRules", func(t *testing.T) {
		rules := notifier.getRecurrentRules()
		assert.NotEmpty(t, rules)

		// Check that we have expected rules
		ruleIDs := make([]string, len(rules))
		for i, rule := range rules {
			ruleIDs[i] = rule.ID
		}

		expectedRules := []string{
			"HABITO_MENSAL",
			"INATIVIDADE",
			"APONTAMENTO_DIARIO",
		}

		for _, expectedRule := range expectedRules {
			assert.Contains(t, ruleIDs, expectedRule)
		}
	})

	// Test rule eligibility
	t.Run("RuleEligibility", func(t *testing.T) {
		user := &model.User{ID: "test-user"}

		// Test DIAGNOSTICO_FINANCEIRO rule
		state := &notification.UserActivityState{
			UserID: "test-user",
			OnboardingState: notification.OnboardingState{
				StartedDiagnosisAt:   time.Now().Add(-25 * time.Hour), // Started more than 24h ago
				CompletedDiagnosisAt: time.Time{},                     // Not completed
			},
			NotificationState: notification.NotificationState{
				SentUnique: make(map[string]time.Time), // Not sent before
			},
		}

		rules := notifier.getUniqueRules()
		var diagnosticoRule *NotificationRule
		for _, rule := range rules {
			if rule.ID == "DIAGNOSTICO_FINANCEIRO" {
				diagnosticoRule = &rule
				break
			}
		}

		assert.NotNil(t, diagnosticoRule)
		assert.True(t, diagnosticoRule.IsEligible(user, state, time.Now()))

		// Test when already sent
		state.NotificationState.SentUnique["DIAGNOSTICO_FINANCEIRO"] = time.Now()
		assert.False(t, diagnosticoRule.IsEligible(user, state, time.Now()))
	})

	// Test best rule selection
	t.Run("BestRuleSelection", func(t *testing.T) {
		user := &model.User{ID: "test-user"}
		state := &notification.UserActivityState{
			UserID: "test-user",
			OnboardingState: notification.OnboardingState{
				StartedDiagnosisAt:   time.Now().Add(-25 * time.Hour),
				CompletedDiagnosisAt: time.Time{},
			},
			NotificationState: notification.NotificationState{
				SentUnique: make(map[string]time.Time),
			},
		}

		rules := notifier.getUniqueRules()
		bestRule := notifier.findBestNotificationRule(user, state, rules, time.Now())

		assert.NotNil(t, bestRule)
		assert.Equal(t, "DIAGNOSTICO_FINANCEIRO", bestRule.ID) // Should be highest priority eligible rule
	})
}

// TestActivityTracking tests the activity tracking methods
func TestActivityTracking(t *testing.T) {
	mockActivityRepo := &MockActivityStateRepository{}
	mockUserRepo := &MockUserRepository{}

	notifier := &FirebasePushNotifier{
		activityStateRepo: mockActivityRepo,
		userRepo:          mockUserRepo,
	}

	ctx := context.Background()
	userID := "test-user"

	t.Run("UpdateLastLogin", func(t *testing.T) {
		mockActivityRepo.On("UpdateLastLogin", ctx, userID).Return(nil)

		err := notifier.UpdateLastLogin(ctx, userID)
		assert.NoError(t, err)

		mockActivityRepo.AssertExpectations(t)
	})

	t.Run("UpdateLastExpenseLogged", func(t *testing.T) {
		mockActivityRepo.On("UpdateLastExpenseLogged", ctx, userID).Return(nil)

		err := notifier.UpdateLastExpenseLogged(ctx, userID)
		assert.NoError(t, err)

		mockActivityRepo.AssertExpectations(t)
	})

	t.Run("UpdateLastProgressionActivity", func(t *testing.T) {
		mockActivityRepo.On("UpdateLastProgressionActivity", ctx, userID).Return(nil)

		err := notifier.UpdateLastProgressionActivity(ctx, userID)
		assert.NoError(t, err)

		mockActivityRepo.AssertExpectations(t)
	})

	t.Run("UpdateOnboardingEvent", func(t *testing.T) {
		eventType := "STARTED_DIAGNOSIS"
		mockActivityRepo.On("UpdateOnboardingEvent", ctx, userID, eventType).Return(nil)

		err := notifier.UpdateOnboardingEvent(ctx, userID, eventType)
		assert.NoError(t, err)

		mockActivityRepo.AssertExpectations(t)
	})
}
