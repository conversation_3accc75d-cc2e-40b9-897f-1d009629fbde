package notification

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

func TestNew(t *testing.T) {
	// Test that New() creates a service without panicking
	service := New(nil, nil, nil, nil)
	if service == nil {
		t.Fatal("New() returned nil service")
	}
}

func TestMockNotifier(t *testing.T) {
	ctx := context.Background()
	mock := NewMockNotifier()

	// Test password reset
	err := mock.SendPasswordReset(ctx, "<EMAIL>", "Test User", "http://example.com/reset")
	if err != nil {
		t.Fatalf("SendPasswordReset failed: %v", err)
	}

	// Verify email was sent
	if !mock.HasSentEmailTo("<EMAIL>") {
		t.Error("Email was not sent to expected recipient")
	}

	if !mock.HasSentEmailOfType("password_reset") {
		t.Error("Password reset email was not sent")
	}

	// Test subscription confirmation
	err = mock.SendSubscriptionConfirmation(ctx, "<EMAIL>", "Test User", "Premium")
	if err != nil {
		t.Fatalf("SendSubscriptionConfirmation failed: %v", err)
	}

	// Test subscription cancellation
	err = mock.SendSubscriptionCancellation(ctx, "<EMAIL>", "Test User", "Premium")
	if err != nil {
		t.Fatalf("SendSubscriptionCancellation failed: %v", err)
	}

	// Test new user welcome
	err = mock.SendNewUserWelcome(ctx, "<EMAIL>", "Test User", "http://example.com/setup", "Premium")
	if err != nil {
		t.Fatalf("SendNewUserWelcome failed: %v", err)
	}

	// Test generic email
	err = mock.SendGenericEmail(ctx, "<EMAIL>", "Test User", "Test Subject", "Test Content")
	if err != nil {
		t.Fatalf("SendGenericEmail failed: %v", err)
	}

	// Verify total count
	if mock.GetSentEmailsCount() != 5 {
		t.Errorf("Expected 5 emails sent, got %d", mock.GetSentEmailsCount())
	}

	// Test failure mode
	mock.SetShouldFail(true, nil)
	err = mock.SendPasswordReset(ctx, "<EMAIL>", "Test User", "http://example.com/reset")
	if err == nil {
		t.Error("Expected error when mock is set to fail, but got nil")
	}
}

func TestConfigFromEnv(t *testing.T) {
	// Test that loadConfigFromEnv doesn't panic
	config := loadConfigFromEnv()
	if config == nil {
		t.Fatal("loadConfigFromEnv returned nil")
	}

	// Test default values
	if config.BrevoSenderEmail != "<EMAIL>" {
		t.Errorf("Expected default Brevo sender email, got %s", config.BrevoSenderEmail)
	}

	if config.ApiURL != "http://localhost:8080" {
		t.Errorf("Expected default app URL, got %s", config.ApiURL)
	}
}

func TestNotificationError(t *testing.T) {
	// Test that errors are properly created using the standard error package
	err := errors.New(errors.Service, "test message", errors.Internal, nil)
	if err == nil {
		t.Fatal("errors.New returned nil")
	}

	expectedMsg := "service: test message (kind:internal)"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message %q, got %q", expectedMsg, err.Error())
	}
}

func TestProviderAccess(t *testing.T) {
	service := New(nil, nil, nil, nil)

	// Test Gmail provider access
	gmailNotifier := service.GmailNotifier
	// Gmail notifier might be nil if not configured, which is expected
	if gmailNotifier != nil {
		// If configured, it should be a valid notifier
		// We can't test actual sending without credentials, but we can verify the interface
		_ = gmailNotifier
	}

	// Test Brevo provider access
	brevoNotifier := service.BrevoNotifier
	// Brevo notifier might be nil if not configured, which is expected
	if brevoNotifier != nil {
		// If configured, it should be a valid notifier
		// We can't test actual sending without credentials, but we can verify the interface
		_ = brevoNotifier
	}

	// Test that we can access providers independently
	if gmailNotifier != nil && brevoNotifier != nil {
		// Both providers are different instances
		if gmailNotifier == brevoNotifier {
			t.Error("Gmail and Brevo notifiers should be different instances")
		}
	}
}
