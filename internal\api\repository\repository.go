package repository

import (
	"sync"

	"github.com/dsoplabs/dinbora-backend/internal/repository/auth/otp"
	"github.com/dsoplabs/dinbora-backend/internal/repository/auth/session"
	"github.com/dsoplabs/dinbora-backend/internal/repository/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/firebase"
	"github.com/dsoplabs/dinbora-backend/internal/repository/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/league" // Added league repository import
	"github.com/dsoplabs/dinbora-backend/internal/repository/notification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	"github.com/dsoplabs/dinbora-backend/internal/repository/vault"
	"go.mongodb.org/mongo-driver/mongo"
)

type RepositoryRegistry struct {
	// Auth
	Session session.Repository
	OTP     otp.Repository

	// Billing
	Billing billing.Repository

	// Content
	Achievement         achievement.Repository
	Investimentcategory investmentcategory.Repository
	Ticker              ticker.Repository
	Trail               trail.Repository
	Wallet              wallet.Repository

	Dashboard  dashboard.Repository
	Dreamboard dreamboard.Repository

	FinancialDNA   financialdna.Repository
	FinancialSheet financialsheet.Repository
	Firebase       firebase.Repository
	Gamification   gamification.Repository
	League         league.Repository // Added League repository
	Notification   notification.Repository

	Progression progression.Repository

	User  user.Repository
	Vault vault.Repository
}

type RepositoryContainer struct {
	repositories map[string]interface{}
	db           *mongo.Database
	mu           sync.RWMutex
}

func NewContainer(db *mongo.Database) *RepositoryContainer {
	return &RepositoryContainer{
		repositories: make(map[string]interface{}),
		db:           db,
	}
}

func (rc *RepositoryContainer) Register(name string, repo interface{}) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.repositories[name] = repo
}

func (rc *RepositoryContainer) Get(name string) interface{} {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	return rc.repositories[name]
}

func (rc *RepositoryContainer) Initialize() *RepositoryRegistry {
	// Lazy initialization of repositories
	lazyInitRepo := func(name string, initFunc func() interface{}) interface{} {
		if existing := rc.Get(name); existing != nil {
			return existing
		}
		repo := initFunc()
		rc.Register(name, repo)
		return repo
	}

	// Initialize all repositories with the database
	// Auth
	sessionRepo := lazyInitRepo("session", func() interface{} {
		return session.NewMongoDB(rc.db)
	}).(session.Repository)
	otpRepo := lazyInitRepo("otp", func() interface{} {
		return otp.NewMongoDB(rc.db)
	}).(otp.Repository)

	// Billing
	billingRepo := lazyInitRepo("billing", func() interface{} {
		return billing.New(rc.db)
	}).(billing.Repository)

	// Content
	achievementRepo := lazyInitRepo("achievement", func() interface{} {
		return achievement.New(rc.db)
	}).(achievement.Repository)
	investmentCategoryRepo := lazyInitRepo("investmentcategory", func() interface{} {
		return investmentcategory.New(rc.db)
	}).(investmentcategory.Repository)
	tickerRepo := lazyInitRepo("ticker", func() interface{} {
		return ticker.New(rc.db)
	}).(ticker.Repository)
	trailRepo := lazyInitRepo("trail", func() interface{} {
		return trail.New(rc.db)
	}).(trail.Repository)
	walletRepo := lazyInitRepo("wallet", func() interface{} {
		return wallet.New(rc.db)
	}).(wallet.Repository)

	dashboardRepo := lazyInitRepo("dashboard", func() interface{} {
		return dashboard.New(rc.db)
	}).(dashboard.Repository)
	dreamboardRepo := lazyInitRepo("dreamboard", func() interface{} {
		return dreamboard.New(rc.db)
	}).(dreamboard.Repository)

	financialDNARepo := lazyInitRepo("financialdna", func() interface{} {
		return financialdna.New(rc.db)
	}).(financialdna.Repository)

	financialSheetRepo := lazyInitRepo("financialsheet", func() interface{} {
		return financialsheet.New(rc.db)
	}).(financialsheet.Repository)

	firebaseRepo := lazyInitRepo("firebase", func() interface{} {
		return firebase.New(rc.db)
	}).(firebase.Repository)

	gamificationRepo := lazyInitRepo("gamification", func() interface{} {
		return gamification.New(rc.db)
	}).(gamification.Repository)

	progressionRepo := lazyInitRepo("progression", func() interface{} {
		return progression.New(rc.db)
	}).(progression.Repository)

	userRepo := lazyInitRepo("user", func() interface{} {
		return user.New(rc.db)
	}).(user.Repository)
	vaultRepo := lazyInitRepo("vault", func() interface{} {
		return vault.New(rc.db)
	}).(vault.Repository)
	leagueRepo := lazyInitRepo("league", func() interface{} {
		return league.New(rc.db) // Corrected constructor to package.New
	}).(league.Repository)

	notificationRepo := lazyInitRepo("notification", func() interface{} {
		return notification.New(rc.db)
	}).(notification.Repository)

	return &RepositoryRegistry{
		Session:             sessionRepo,
		OTP:                 otpRepo,
		Billing:             billingRepo,
		Achievement:         achievementRepo,
		Investimentcategory: investmentCategoryRepo,
		Ticker:              tickerRepo,
		Trail:               trailRepo,
		Wallet:              walletRepo,
		Dashboard:           dashboardRepo,
		Dreamboard:          dreamboardRepo,
		FinancialDNA:        financialDNARepo,
		FinancialSheet:      financialSheetRepo,
		Firebase:            firebaseRepo,
		Gamification:        gamificationRepo,
		Notification:        notificationRepo,
		Progression:         progressionRepo,
		User:                userRepo,
		Vault:               vaultRepo,
		League:              leagueRepo,
	}
}
